const { Device, KioskGroup, KioskSetting, Facility, Building, Floor, Room, Identity } = require("../models");
const { paginate } = require("../models/plugins/paginate.plugin");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");
const { generateDeviceQRCode } = require("../services/qrcode.service");
const { generateAuthTokens, getUserPermissions } = require("../services/auth.service");

/**
 * Get all kiosk groups (without pagination).
 *
 * @async
 * @function fetchKioskGroups
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with an array of kiosk group data.
 */
exports.fetchKioskGroups = catchAsync(async (req, res, next) => {
  const kioskGroups = await KioskGroup.findAll({
    attributes: ["kiosk_group_id", "name"],
  });

  sendSuccess(res, "Kiosk groups fetched successfully", httpStatus.OK, { data: kioskGroups });
});

/**
 * Get all devices for a specific facility (paginated).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} req.query - Query parameters for pagination (page and limit).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with device data, including kiosk group details.
 */
exports.index = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    where: { facility_id: facilityId },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["name"],
      },
      {
        model: Building,
        as: "building",
        attributes: ["name", "building_code"],
      },
      {
        model: Floor,
        as: "floor",
        attributes: ["floor_number"],
      },
      {
        model: Room,
        as: "room",
        attributes: ["room_number"],
      },
    ],
  };

  const result = await paginate(Device, queryOptions, paginationOptions);
  sendSuccess(res, "Devices retrieved successfully", httpStatus.OK, result);
});

/**
 * Get a single device by its ID for a specific facility.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and deviceId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the device details with kiosk group information or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res, next) => {
  const { facilityId, deviceId } = req.params;
  const device = await Device.findOne({
    where: { device_id: deviceId, facility_id: facilityId },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["name"],
      },
      {
        model: Building,
        as: "building",
        attributes: ["name"],
      },
      {
        model: Floor,
        as: "floor",
        attributes: ["floor_number"],
      },
      {
        model: Room,
        as: "room",
        attributes: ["room_number"],
      },
    ],
  });
  if (!device) {
    return sendError(res, "Device not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Device retrieved successfully", httpStatus.OK, device);
});

/**
 * Create a new device under a specific facility.
 * Uses the entire validated request body.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} req.body - Contains the device details.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created device data including kiosk group details.
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;

  // Merge facility_id into the request body data if not already provided
  const deviceData = {
    ...req.body,
    facility_id: req.body.facility_id || facilityId
  };

  const device = await Device.create(deviceData, { transaction });

  // Generate QR code for the device
  const qrCodeDataURL = await generateDeviceQRCode(device.device_id);

  // Update device with QR code image
  device.qr_code = qrCodeDataURL;
  await device.save({ transaction });

  sendSuccess(res, "Device created successfully", httpStatus.CREATED, {
    ...device.dataValues,
  });
});

/**
 * Update device details.
 * Uses the whole validated request body.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and deviceId.
 * @param {Object} req.body - Contains device fields to update.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response or a 404 error if the device is not found.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, deviceId } = req.params;

  // Find the existing device
  const device = await Device.findOne({
    where: {
      device_id: deviceId,
      facility_id: facilityId,
    },
  });

  if (!device) return sendError(res, "Device not found", httpStatus.NOT_FOUND);

  // Perform the update
  const [updated] = await Device.update(req.body, {
    where: { device_id: deviceId, facility_id: facilityId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Failed to update device", httpStatus.BAD_REQUEST);
  }

  sendSuccess(res, "Device updated successfully", httpStatus.OK);
});

/**
 * Delete a device by its ID for a specific facility.
 *
 * @async
 * @function remove
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and deviceId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response confirming deletion or a 404 error if not found.
 */
exports.remove = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, deviceId } = req.params;

  // Find the device and verify it belongs to the specified facility
  const device = await Device.findOne({
    where: { device_id: deviceId, facility_id: facilityId },
    transaction,
  });

  if (!device) {
    return sendError(res, "Device not found for the given facility", httpStatus.NOT_FOUND);
  }

  // Delete the device
  await device.destroy({ transaction });
  sendSuccess(res, "Device deleted successfully", httpStatus.OK, { device_id: deviceId });
});

/**
 * Generate QR code for an existing device.
 *
 * @async
 * @function generateQR
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and deviceId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the updated device with QR code or a 404 error if not found.
 */
exports.generateQR = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, deviceId } = req.params;

  // Find the device
  const device = await Device.findOne({
    where: {
      device_id: deviceId,
      facility_id: facilityId,
    },
  });

  if (!device) return sendError(res, "Device not found", httpStatus.NOT_FOUND);

  // Generate QR code for the device
  const qrCodeDataURL = await generateDeviceQRCode(device.device_id);

  // Update device with QR code image
  device.qr_code = qrCodeDataURL;
  await device.save({ transaction });

  sendSuccess(res, "QR code generated successfully", httpStatus.OK, {
    device_id: device.device_id,
    name: device.name,
    qr_code: device.qr_code,
  });
});

/**
 * Authenticate device after QR code scan.
 * Sets device fingerprint and returns kiosk user tokens.
 *
 * @async
 * @function authenticate
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains deviceId.
 * @param {Object} req.body - Contains fingerprint_data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends access/refresh tokens and device config.
 */
exports.authenticate = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { deviceId } = req.params;
  const { fingerprint_data } = req.body;

  if (!fingerprint_data) {
    return sendError(res, "Fingerprint data is required", httpStatus.BAD_REQUEST);
  }

  // Find the device
  const device = await Device.findOne({
    where: { device_id: deviceId },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["name"],
      },
    ],
  });

  if (!device) {
    return sendError(res, "Device not found", httpStatus.NOT_FOUND);
  }

  // Update device identifier with fingerprint data
  device.identifier = fingerprint_data;
  await device.save({ transaction });

  // Get kiosk user identity
  const kioskIdentity = await Identity.findOne({
    where: { email: "<EMAIL>" },
    transaction,
  });

  if (!kioskIdentity) {
    return sendError(res, "Kiosk user not found", httpStatus.NOT_FOUND);
  }

  // Generate tokens for kiosk user
  const permissions = await getUserPermissions(kioskIdentity);
  const authResult = await generateAuthTokens(kioskIdentity, transaction, permissions);

  sendSuccess(res, "Device authenticated successfully", httpStatus.OK, {
    access_token: authResult.tokens.access.token,
    refresh_token: authResult.tokens.refresh.token,
    device_config: {
      device_id: device.device_id,
      name: device.name,
      identifier: device.identifier,
      kiosk_group: device.kiosk_group,
      facility: device.facility,
    },
    permissions: authResult.permissions,
  });
});
