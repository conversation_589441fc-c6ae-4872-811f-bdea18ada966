const Joi = require("joi");
const { exists, unique } = require("./custom.validation");

// Reusable identifiers
const facilityId = Joi.string().required().external(exists("Facility", "facility_id"));
const deviceId = Joi.string().required().external(exists("Device", "device_id"));

// Optional identifiers
const facility_id = Joi.string().optional().external(exists("Facility", "facility_id"));
const building_id = Joi.string().optional().external(exists("Building", "building_id"));
const floor_id = Joi.string().optional().external(exists("Floor", "floor_id"));
const room_id = Joi.string().optional().external(exists("Room", "room_id"));
const kiosk_group_id = Joi.string().required().external(exists("KioskGroup", "kiosk_group_id"));

const create = {
  params: Joi.object().keys({
    facilityId,
  }),
  body: Joi.object().keys({
    name: Joi.string().max(100).required(),
    identifier: Joi.string().max(100).optional().external(unique("Device", "identifier")),
    kiosk_group_id,
    facility_id,
    building_id,
    floor_id,
    room_id,
  }),
};

const device = {
  params: Joi.object().keys({
    facilityId,
    deviceId,
  }),
};

const update = {
  params: Joi.object().keys({
    facilityId,
    deviceId,
  }),
  body: Joi.object()
    .keys({
      name: Joi.string().max(100).optional(),
      identifier: Joi.string().max(100).optional(),
      kiosk_group_id: Joi.string().optional().external(exists("KioskGroup", "kiosk_group_id")),
      facility_id,
      building_id,
      floor_id,
      room_id,
    })
    .min(1),
};

const facility = {
  params: Joi.object().keys({
    facilityId,
  }),
};

const remove = {
  params: Joi.object().keys({
    facilityId,
    deviceId,
  }),
};

module.exports = {
  create,
  device,
  update,
  facility,
  remove,
};
