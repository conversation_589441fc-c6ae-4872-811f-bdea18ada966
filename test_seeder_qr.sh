#!/bin/bash

# Test QR Code Seeder and Authentication Flow
BASE_URL="http://localhost:3001/api"

echo "🌱 Step 1: Run seeder to create devices with QR codes..."
echo "Note: Make sure to run 'npm run db:seed' to populate test data"

echo -e "\n🔐 Step 2: Login as admin..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"Pa$$w0rd!"}')

TOKEN=$(echo $LOGIN_RESPONSE | jq -r '.data.tokens.access.token')
if [ "$TOKEN" = "null" ]; then
  echo "❌ Login failed. Make sure server is running and credentials are correct."
  exit 1
fi
echo "✅ Admin login successful"

echo -e "\n📋 Step 3: Get devices from seeder..."
FACILITIES_RESPONSE=$(curl -s -X GET "$BASE_URL/facility" \
  -H "Authorization: Bearer $TOKEN")

FACILITY_ID=$(echo $FACILITIES_RESPONSE | jq -r '.data.data[0].facility_id')
echo "✅ Using facility: $FACILITY_ID"

DEVICES_RESPONSE=$(curl -s -X GET "$BASE_URL/facility/devices/$FACILITY_ID" \
  -H "Authorization: Bearer $TOKEN")

DEVICE_COUNT=$(echo $DEVICES_RESPONSE | jq -r '.data.data | length')
echo "✅ Found $DEVICE_COUNT devices from seeder"

if [ "$DEVICE_COUNT" -eq 0 ]; then
  echo "❌ No devices found. Please run the seeder first: npm run db:seed"
  exit 1
fi

# Get first device for testing
DEVICE_ID=$(echo $DEVICES_RESPONSE | jq -r '.data.data[0].device_id')
DEVICE_NAME=$(echo $DEVICES_RESPONSE | jq -r '.data.data[0].name')
HAS_QR=$(echo $DEVICES_RESPONSE | jq -r '.data.data[0].qr_code != null')

echo "📱 Testing device: $DEVICE_NAME ($DEVICE_ID)"
echo "📱 QR Code present: $HAS_QR"

echo -e "\n🔄 Step 4: Test kiosk device authentication..."
FINGERPRINT="test_seeder_fingerprint_$(date +%s)"

AUTH_RESPONSE=$(curl -s -X POST "$BASE_URL/kiosk/device/authenticate/$DEVICE_ID" \
  -H "Content-Type: application/json" \
  -d "{\"fingerprint_data\":\"$FINGERPRINT\"}")

AUTH_SUCCESS=$(echo $AUTH_RESPONSE | jq -r '.status')
if [ "$AUTH_SUCCESS" = "true" ]; then
  echo "✅ Device authentication successful"
  echo "🔑 Access Token: $(echo $AUTH_RESPONSE | jq -r '.data.access_token != null')"
  echo "🔄 Refresh Token: $(echo $AUTH_RESPONSE | jq -r '.data.refresh_token != null')"
  echo "📋 Permissions: $(echo $AUTH_RESPONSE | jq -r '.data.permissions[]')"
  echo "🖥️ Device Config: $(echo $AUTH_RESPONSE | jq -r '.data.device_config.name')"
else
  echo "❌ Device authentication failed"
  echo "Error: $(echo $AUTH_RESPONSE | jq -r '.message')"
fi

echo -e "\n🎉 Seeder QR Code Test Complete!"
echo "Device ID: $DEVICE_ID"
echo "Fingerprint: $FINGERPRINT"
