const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

// Test credentials from seeder
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'Pa$$w0rd!'
};

async function testDeviceQRFlow() {
  try {
    console.log('🔐 Step 1: Login as admin...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, ADMIN_CREDENTIALS);
    const adminToken = loginResponse.data.data.tokens.access.token;
    console.log('✅ Admin login successful');

    const headers = {
      'Authorization': `Bearer ${adminToken}`,
      'Content-Type': 'application/json'
    };

    console.log('\n📋 Step 2: Get facilities...');
    const facilitiesResponse = await axios.get(`${BASE_URL}/facility`, { headers });
    const facilities = facilitiesResponse.data.data.data;
    
    if (facilities.length === 0) {
      console.log('❌ No facilities found. Please create a facility first.');
      return;
    }
    
    const facilityId = facilities[0].facility_id;
    console.log(`✅ Using facility: ${facilities[0].name} (${facilityId})`);

    console.log('\n📋 Step 3: Get kiosk groups...');
    const kioskGroupsResponse = await axios.get(`${BASE_URL}/master-data/kiosk-groups`, { headers });
    const kioskGroups = kioskGroupsResponse.data.data.data;

    if (kioskGroups.length === 0) {
      console.log('❌ No kiosk groups found. Please create a kiosk group first.');
      return;
    }

    const kioskGroupId = kioskGroups[0].kiosk_group_id;
    console.log(`✅ Using kiosk group: ${kioskGroups[0].name} (${kioskGroupId})`);

    console.log('\n🖥️ Step 4: Create device with QR code...');
    const deviceData = {
      name: 'Test QR Device',
      kiosk_group_id: kioskGroupId,
      facility_id: facilityId
    };

    const createDeviceResponse = await axios.post(
      `${BASE_URL}/facility/devices/${facilityId}`, 
      deviceData, 
      { headers }
    );
    
    const device = createDeviceResponse.data.data;
    console.log(`✅ Device created: ${device.name} (${device.device_id})`);
    console.log(`📱 QR Code generated: ${device.qr_code ? 'Yes' : 'No'}`);

    console.log('\n🔄 Step 5: Update device to regenerate QR code...');
    const updateResponse = await axios.patch(
      `${BASE_URL}/facility/devices/${facilityId}/${device.device_id}`,
      { generate_qr: true },
      { headers }
    );
    
    console.log('✅ QR code regenerated successfully');
    console.log(`📱 New QR Code: ${updateResponse.data.data.qr_code ? 'Generated' : 'Failed'}`);

    console.log('\n📱 Step 6: Test device authentication (QR scan simulation)...');
    const fingerprintData = 'test_device_fingerprint_' + Date.now();
    
    const authResponse = await axios.post(
      `${BASE_URL}/device/authenticate/${device.device_id}`,
      { fingerprint_data: fingerprintData }
    );
    
    const authData = authResponse.data.data;
    console.log('✅ Device authentication successful');
    console.log(`🔑 Access Token: ${authData.access_token ? 'Generated' : 'Failed'}`);
    console.log(`🔄 Refresh Token: ${authData.refresh_token ? 'Generated' : 'Failed'}`);
    console.log(`📋 Permissions: ${authData.permissions.join(', ')}`);
    console.log(`🖥️ Device Config: ${authData.device_config.name}`);

    console.log('\n🎉 All tests passed successfully!');
    console.log('\n📋 Summary:');
    console.log(`- Device ID: ${device.device_id}`);
    console.log(`- QR Code: Generated`);
    console.log(`- Fingerprint: ${fingerprintData}`);
    console.log(`- Kiosk Token: Generated`);
    console.log(`- Permissions: ${authData.permissions.join(', ')}`);

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.data) {
      console.error('Response data:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// Run the test
testDeviceQRFlow();
