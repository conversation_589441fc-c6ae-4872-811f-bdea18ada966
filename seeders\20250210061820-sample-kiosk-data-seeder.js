"use strict";

const { v4: uuidv4 } = require("uuid");
const logger = require("../config/logger");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      logger.info("Starting comprehensive kiosk settings seeding...");

      // Step 1: Create Kiosk Groups
      const kioskGroups = [
        {
          kiosk_group_id: uuidv4(),
          name: "Main Lobby Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Emergency Department Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_group_id: uuidv4(),
          name: "Outpatient Clinic Kiosks",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("kiosk_group", kioskGroups, { transaction });
      logger.info(`Created ${kioskGroups.length} kiosk groups`);

      // Step 2: Create NDA Templates
      const ndaTemplates = [
        {
          nda_template_id: uuidv4(),
          name: "Standard Hospital NDA",
          version: 1,
          document_text: `Confidentiality and Non-Disclosure Agreement
Security and confidentiality are matters of concern for all persons who have access to Mayo Clinic Jacksonville or
St. Luke’s Hospital (for the purpose of efficiency, collectively referred to herein as “Mayo”) confidential patient
information and confidential information about the business and financial interests of “Mayo” (referred to as
“Confidential Information” in this Agreement). Each person accessing Mayo Confidential Information holds a
position of trust relative to this information and must recognize the responsibilities entrusted in preserving the
security and confidentiality of this information. Therefore, all persons who are authorized to access Confidential
Information must read and comply with all Mayo Policies.
As a condition to receiving access to information, I, the undersigned, agree to comply with the following terms:
1. I will not at any time during or after my affiliation with Mayo disclose Confidential Information to which I have or
had access in any form (i.e., electronic media, paper, microfilm, verbal etc.) to any unauthorized individuals.
2. My computer log-in is equivalent to my LEGAL SIGNATURE and I will not share or disclose this code to anyone
or allow anyone to access any application using my log-in.
3. I will not access any medical record I am not legally authorized to, including but not limited to the medical
record of any family member or co-worker.
4. I will utilize and access only the minimum amount of information necessary for performance of my job.
5. I will not access or request data on patients for whom I have no clinical/professional relationship and/or
legitimate Mayo business purpose. If I have reason to believe that the confidentiality of my user log-in has
been compromised, I will immediately ensure that the password is changed by the approved procedure for
password name change.
6. I will respect the confidentiality of any reports and handle, store and dispose of these reports appropriately.
7. I will utilize the Privacy Curtain or suspend access when leaving a workstation to prevent unauthorized access.
8. I will not install or operate any non-licensed software on any Mayo computer.
9. I will comply with all policies and procedures and other rules relating to confidentiality of information and log-ins.
10. I understand it is against Mayo policy to electronically communicate clinical information to patients or others
outside of the Mayo network.
11. I am responsible for all e-mail messages generated from my e-mail account.
12. I understand that the use of e-mail is for business purposes, however limited personal use is acceptable.
13. I understand that the e-mail administrator may monitor my e-mail if non-compliance with the electronic
messaging policies is suspected.
I understand that Confidential Information is protected in every form, such as written records and correspondence,
oral communications, and electronic information systems and acknowledge my responsibility in preserving its
confidentiality. I understand there are disciplinary procedures in place for handling breaches of confidentiality. I
have read and understand the above Confidentiality and Non-Disclosure Agreement. I understand that my use of
Mayo information will be monitored to ensure compliance with this agreement. I further understand that if I violate
any of the above terms, I may be subject to disciplinary action, including but not limited to discharge, loss of
privileges to access information, termination of contract or any other remedy available to Mayo Clinic Jacksonville
or St. Luke’s Hospital.
By signing this, I agree that I have read, understand and will comply with this Agreement`,
          jurisdiction: "California",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          nda_template_id: uuidv4(),
          name: "Emergency Department NDA",
          version: 1,
          document_text: `Copyright © 2020 NonDisclosureAgreement.com. All Rights Reserved. Page 1 of 2
NON-DISCLOSURE AGREEMENT (NDA)
This Nondisclosure Agreement or ("Agreement") has been entered into on the date of
______________________________ and is by and between:
Party Disclosing Information: ______________________________ with a mailing address of
____________________________________________________________ (“Disclosing Party”).
Party Receiving Information: ______________________________ with a mailing address of
____________________________________________________________ (“Receiving Party”).
For the purpose of preventing the unauthorized disclosure of Confidential Information as defined
below. The parties agree to enter into a confidential relationship concerning the disclosure of
certain proprietary and confidential information ("Confidential Information").
1. Definition of Confidential Information. For purposes of this Agreement, "Confidential
Information" shall include all information or material that has or could have commercial value or
other utility in the business in which Disclosing Party is engaged. If Confidential Information is in
written form, the Disclosing Party shall label or stamp the materials with the word "Confidential"
or some similar warning. If Confidential Information is transmitted orally, the Disclosing Party
shall promptly provide writing indicating that such oral communication constituted Confidential
Information.
2. Exclusions from Confidential Information. Receiving Party's obligations under this
Agreement do not extend to information that is: (a) publicly known at the time of disclosure or
subsequently becomes publicly known through no fault of the Receiving Party; (b) discovered or
created by the Receiving Party before disclosure by Disclosing Party; (c) learned by the
Receiving Party through legitimate means other than from the Disclosing Party or Disclosing
Party's representatives; or (d) is disclosed by Receiving Party with Disclosing Party's prior
written approval.
3. Obligations of Receiving Party. Receiving Party shall hold and maintain the Confidential
Information in strictest confidence for the sole and exclusive benefit of the Disclosing Party.
Receiving Party shall carefully restrict access to Confidential Information to employees,
contractors and third parties as is reasonably required and shall require those persons to sign
nondisclosure restrictions at least as protective as those in this Agreement. Receiving Party
shall not, without the prior written approval of Disclosing Party, use for Receiving Party's benefit,
publish, copy, or otherwise disclose to others, or permit the use by others for their benefit or to
the detriment of Disclosing Party, any Confidential Information. Receiving Party shall return to
Disclosing Party any and all records, notes, and other written, printed, or tangible materials in its
possession pertaining to Confidential Information immediately if Disclosing Party requests it in
writing.
4. Time Periods. The nondisclosure provisions of this Agreement shall survive the termination
of this Agreement and Receiving Party's duty to hold Confidential Information in confidence
shall remain in effect until the Confidential Information no longer qualifies as a trade secret or
until Disclosing Party sends Receiving Party written notice releasing Receiving Party from this
Agreement, whichever occurs first.
Copyright © 2020 NonDisclosureAgreement.com. All Rights Reserved. Page 2 of 2
5. Relationships. Nothing contained in this Agreement shall be deemed to constitute either
party a partner, joint venture or employee of the other party for any purpose.
6. Severability. If a court finds any provision of this Agreement invalid or unenforceable, the
remainder of this Agreement shall be interpreted so as best to affect the intent of the parties.
7. Integration. This Agreement expresses the complete understanding of the parties with
respect to the subject matter and supersedes all prior proposals, agreements, representations,
and understandings. This Agreement may not be amended except in writing signed by both
parties.
8. Waiver. The failure to exercise any right provided in this Agreement shall not be a waiver of
prior or subsequent rights.
9. Notice of Immunity. Employee is provided notice that an individual shall not be held
criminally or civilly liable under any federal or state trade secret law for the disclosure of a trade
secret that is made (i) in confidence to a federal, state, or local government official, either
directly or indirectly, or to an attorney; and (ii) solely for the purpose of reporting or investigating
a suspected violation of law; or is made in a complaint or other document filed in a lawsuit or
other proceeding, if such filing is made under seal. An individual who files a lawsuit for
retaliation by an employer for reporting a suspected violation of law may disclose the trade
secret to the attorney of the individual and use the trade secret information in the court
proceeding, if the individual (i) files any document containing the trade secret under seal; and (ii)
does not disclose the trade secret, except pursuant to court order.
This Agreement and each party's obligations shall be binding on the representatives, assigns
and successors of such party. Each party has signed this Agreement through its authorized
representative.
DISCLOSING PARTY
Signature: _____________________________________________________
Typed or Printed Name: ___________________________ Date: _______________
RECEIVING PARTY
Signature: _____________________________________________________
Typed or Printed Name: ___________________________ Date: _______________`,
          jurisdiction: "California",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("nda_template", ndaTemplates, { transaction });
      logger.info(`Created ${ndaTemplates.length} NDA templates`);

      // Step 3: Create 6 predefined Kiosk Settings with NDA templates
      const kioskSettings = [
        {
          kiosk_setting_id: uuidv4(),
          config_key: "show_nda",
          config_group: "display_settings",
          name: "Show NDA",
          description: "For showing NDA agreement to users",
          default_config_value: "true",
          nda_template_id: ndaTemplates[0].nda_template_id, // Standard Hospital NDA
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_setting_id: uuidv4(),
          config_key: "doctors_office_visit",
          config_group: "visit_types",
          name: "Doctor's Office Visit",
          description: "Enable doctor's office visit option",
          default_config_value: "true",
          nda_template_id: ndaTemplates[0].nda_template_id, // Standard Hospital NDA
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_setting_id: uuidv4(),
          config_key: "show_expedite_check_in",
          config_group: "display_settings",
          name: "Show Expedite Check In",
          description: "For showing expedite check-in option",
          default_config_value: "true",
          nda_template_id: ndaTemplates[1].nda_template_id, // Emergency Department NDA
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_setting_id: uuidv4(),
          config_key: "show_walk_in_guest",
          config_group: "guest_settings",
          name: "Show Walk In Guest",
          description: "For showing walk-in guest option",
          default_config_value: "false",
          nda_template_id: ndaTemplates[1].nda_template_id, // Emergency Department NDA
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_setting_id: uuidv4(),
          config_key: "guest_verification",
          config_group: "security_settings",
          name: "Guest Verification",
          description: "For enabling guest verification process",
          default_config_value: "false",
          nda_template_id: ndaTemplates[0].nda_template_id, // Standard Hospital NDA
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          kiosk_setting_id: uuidv4(),
          config_key: "re_check_in",
          config_group: "check_in_settings",
          name: "Re Check In",
          description: "For enabling re-check-in functionality",
          default_config_value: "false",
          nda_template_id: ndaTemplates[0].nda_template_id, // Standard Hospital NDA
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("kiosk_setting", kioskSettings, { transaction });
      logger.info(`Created ${kioskSettings.length} kiosk settings`);

      // Step 4: Create Kiosk Group Settings (linking groups to settings)
      const kioskGroupSettings = [];

      // Main Lobby Kiosks - all settings enabled
      kioskSettings.forEach((setting) => {
        kioskGroupSettings.push({
          kiosk_group_setting_id: uuidv4(),
          kiosk_group_id: kioskGroups[0].kiosk_group_id,
          kiosk_setting_id: setting.kiosk_setting_id,
          config_value: setting.default_config_value,
          created_at: new Date(),
          updated_at: new Date(),
        });
      });

      // Emergency Department Kiosks - mixed configuration
      kioskSettings.forEach((setting) => {
        let configValue = setting.default_config_value;
        if (setting.config_key === "show_walk_in_guest") configValue = "true";
        if (setting.config_key === "guest_verification") configValue = "true";

        kioskGroupSettings.push({
          kiosk_group_setting_id: uuidv4(),
          kiosk_group_id: kioskGroups[1].kiosk_group_id,
          kiosk_setting_id: setting.kiosk_setting_id,
          config_value: configValue,
          created_at: new Date(),
          updated_at: new Date(),
        });
      });

      // Outpatient Clinic Kiosks - minimal configuration
      kioskSettings.forEach((setting) => {
        let configValue = "false";
        if (setting.config_key === "show_nda") configValue = "true";
        if (setting.config_key === "doctors_office_visit") configValue = "true";

        kioskGroupSettings.push({
          kiosk_group_setting_id: uuidv4(),
          kiosk_group_id: kioskGroups[2].kiosk_group_id,
          kiosk_setting_id: setting.kiosk_setting_id,
          config_value: configValue,
          created_at: new Date(),
          updated_at: new Date(),
        });
      });

      await queryInterface.bulkInsert("kiosk_group_setting", kioskGroupSettings, { transaction });
      logger.info(`Created ${kioskGroupSettings.length} kiosk group settings`);

      // Step 5: Get existing facilities or create sample ones
      let facilities = await queryInterface.sequelize.query(
        "SELECT facility_id, name FROM facility LIMIT 3;",
        { type: Sequelize.QueryTypes.SELECT, transaction }
      );

      if (facilities.length === 0) {
        // Create sample facilities if none exist
        const sampleFacilities = [
          {
            facility_id: uuidv4(),
            name: "Central Hospital",
            address: "123 Main St",
            city: "Healthcare City",
            state: "CA",
            zip_code: "90210",
            phone: "555-0100",
            created_at: new Date(),
            updated_at: new Date(),
          },
        ];
        await queryInterface.bulkInsert("facility", sampleFacilities, { transaction });
        facilities = sampleFacilities;
        logger.info(`Created ${sampleFacilities.length} sample facilities`);
      }

      // Step 6: Create Buildings
      const buildings = [
        {
          building_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          name: "Main Building",
          building_code: "MB001",
          status: 0, // Active
          type: 1, // Commercial
          occupancy_type: 1, // Business Occupancy
          phone: "555-0101",
          email: "<EMAIL>",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          building_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          name: "Emergency Wing",
          building_code: "EW001",
          status: 0, // Active
          type: 3, // Institutional
          occupancy_type: 0, // Assembly Occupancy
          phone: "555-0102",
          email: "<EMAIL>",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("building", buildings, { transaction });
      logger.info(`Created ${buildings.length} buildings`);

      // Step 7: Create Floors
      const floors = [
        {
          floor_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          building_id: buildings[0].building_id,
          floor_number: 1,
          status: 0, // Active
          occupancy_type: 1, // Business Occupancy
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          floor_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          building_id: buildings[1].building_id,
          floor_number: 1,
          status: 0, // Active
          occupancy_type: 0, // Assembly Occupancy
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("floor", floors, { transaction });
      logger.info(`Created ${floors.length} floors`);

      // Step 8: Create Rooms
      const rooms = [
        {
          room_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          building_id: buildings[0].building_id,
          floor_id: floors[0].floor_id,
          room_number: "101A",
          max_occupancy: 50,
          area: 500,
          primary_contact_name: "Lobby Manager",
          primary_contact_number: "5550101",
          primary_contact_email: "<EMAIL>",
          status: 0, // Active
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          room_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          building_id: buildings[1].building_id,
          floor_id: floors[1].floor_id,
          room_number: "ER01",
          max_occupancy: 20,
          area: 300,
          primary_contact_name: "ER Manager",
          primary_contact_number: "5550102",
          primary_contact_email: "<EMAIL>",
          status: 0, // Active
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          room_id: uuidv4(),
          facility_id: facilities[0].facility_id,
          building_id: buildings[0].building_id,
          floor_id: floors[0].floor_id,
          room_number: "OPC01",
          max_occupancy: 30,
          area: 400,
          primary_contact_name: "Outpatient Manager",
          primary_contact_number: "5550103",
          primary_contact_email: "<EMAIL>",
          status: 0, // Active
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("room", rooms, { transaction });
      logger.info(`Created ${rooms.length} rooms`);

      // Step 9: Create Devices with identifiers
      const devices = [
        {
          device_id: uuidv4(),
          name: "Main Lobby Kiosk 1",
          identifier: "KIOSK_LOBBY_001",
          kiosk_group_id: kioskGroups[0].kiosk_group_id,
          facility_id: facilities[0].facility_id,
          building_id: buildings[0].building_id,
          floor_id: floors[0].floor_id,
          room_id: rooms[0].room_id,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Emergency Department Kiosk 1",
          identifier: "KIOSK_ER_001",
          kiosk_group_id: kioskGroups[1].kiosk_group_id,
          facility_id: facilities[0].facility_id,
          building_id: buildings[1].building_id,
          floor_id: floors[1].floor_id,
          room_id: rooms[1].room_id,
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          device_id: uuidv4(),
          name: "Outpatient Clinic Kiosk 1",
          identifier: "KIOSK_OPC_001",
          kiosk_group_id: kioskGroups[2].kiosk_group_id,
          facility_id: facilities[0].facility_id,
          building_id: buildings[0].building_id,
          floor_id: floors[0].floor_id,
          room_id: rooms[2].room_id,
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("device", devices, { transaction });
      logger.info(`Created ${devices.length} devices`);

      // Step 10: Device Settings removed - NDA templates now handled at kiosk setting level

      // Step 11: Create test patients for kiosk testing
      const testPatients = [
        {
          patient_id: uuidv4(),
          first_name: "John",
          last_name: "Doe",
          birth_date: new Date("1990-01-01"),
          gender: 1,
          email: "<EMAIL>",
          phone: "+**********",
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          patient_id: uuidv4(),
          first_name: "Jane",
          last_name: "Smith",
          birth_date: new Date("1985-05-15"),
          gender: 0,
          email: "<EMAIL>",
          phone: "+**********",
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("patient", testPatients, {
        transaction,
      });
      logger.info(`Created ${testPatients.length} test patients`);

      // Step 12: Create test appointments
      const testAppointments = [
        {
          appointment_id: uuidv4(),
          hl7_appointment_id: 12345,
          patient_id: testPatients[0].patient_id,
          appointment_date: new Date(),
          department: "Cardiology",
          provider_name: "Dr. Johnson",
          status: 0, // Scheduled
          type: 0, // Outpatient
          room: 101,
          facility_id: facilities[0].facility_id,
          arrival_time: new Date(),
          departure_time: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours later
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          appointment_id: uuidv4(),
          hl7_appointment_id: 12346,
          patient_id: testPatients[1].patient_id,
          appointment_date: new Date(),
          department: "Emergency",
          provider_name: "Dr. Wilson",
          status: 0, // Scheduled
          type: 1, // Inpatient
          room: 201,
          facility_id: facilities[0].facility_id,
          arrival_time: new Date(),
          departure_time: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours later
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("appointment", testAppointments, {
        transaction,
      });
      logger.info(`Created ${testAppointments.length} test appointments`);

      // Step 13: Create test patient guests
      const testPatientGuests = [
        {
          patient_guest_id: uuidv4(),
          first_name: "John",
          last_name: "Doe",
          email: "<EMAIL>",
          phone: "+**********",
          patient_id: testPatients[0].patient_id, // Link to John Doe patient
          guest_type: 1, // Visitor
          relationship_type: 1, // Family
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          patient_guest_id: uuidv4(),
          first_name: "Alice",
          last_name: "Johnson",
          email: "<EMAIL>",
          phone: "+**********",
          patient_id: testPatients[0].patient_id, // Link to John Doe patient
          guest_type: 1, // Visitor
          relationship_type: 2, // Friend
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          patient_guest_id: uuidv4(),
          first_name: "Bob",
          last_name: "Wilson",
          email: "<EMAIL>",
          phone: "+**********",
          patient_id: testPatients[1].patient_id, // Link to Jane Smith patient
          guest_type: 2, // Support Person
          relationship_type: 1, // Family
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("patient_guest", testPatientGuests, {
        transaction,
      });
      logger.info(`Created ${testPatientGuests.length} test patient guests`);

      // Step 14: Create test appointment guests with specific PINs for testing
      const testAppointmentGuests = [
        {
          appointment_guest_id: uuidv4(),
          appointment_id: testAppointments[0].appointment_id,
          patient_guest_id: testPatientGuests[0].patient_guest_id,
          facility_id: facilities[0].facility_id,
          guest_pin: "1234", // Test PIN
          status: 0, // Pending Check-In
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          appointment_guest_id: uuidv4(),
          appointment_id: testAppointments[0].appointment_id,
          patient_guest_id: testPatientGuests[1].patient_guest_id,
          facility_id: facilities[0].facility_id,
          guest_pin: "5678", // Test PIN
          status: 4, // Registered
          created_at: new Date(),
          updated_at: new Date(),
        },
        {
          appointment_guest_id: uuidv4(),
          appointment_id: testAppointments[1].appointment_id,
          patient_guest_id: testPatientGuests[2].patient_guest_id,
          facility_id: facilities[0].facility_id,
          guest_pin: "9999", // Test PIN
          status: 0, // Pending Check-In
          created_at: new Date(),
          updated_at: new Date(),
        },
      ];

      await queryInterface.bulkInsert("appointment_guest", testAppointmentGuests, {
        transaction,
      });
      logger.info(`Created ${testAppointmentGuests.length} test appointment guests`);

      console.log("✅ Kiosk test data created successfully!");
      console.log("🧪 Test credentials for kiosk APIs:");
      console.log("   Device ID: KIOSK_LOBBY_001");
      console.log("   Guest PIN: 1234, Name: John Doe");
      console.log("   Guest PIN: 5678, Name: Alice Johnson");
      console.log("   Guest PIN: 9999, Name: Bob Wilson");
      console.log("   Facility ID:", facilities[0].facility_id);
      console.log("   Appointment IDs:", testAppointments.map(a => a.appointment_id));

      await transaction.commit();
      logger.info("Comprehensive kiosk settings seeding completed successfully");
    } catch (error) {
      await transaction.rollback();
      logger.error("Error during kiosk settings seeding:", error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      logger.info("Rolling back comprehensive kiosk settings seeding...");

      // Delete test data first
      await queryInterface.bulkDelete("appointment_guest", null, { transaction });
      await queryInterface.bulkDelete("patient_guest", null, { transaction });
      await queryInterface.bulkDelete("appointment", null, { transaction });
      await queryInterface.bulkDelete("patient", null, { transaction });

      // Delete in reverse order of creation to respect foreign key constraints
      // device_setting table removed - NDA templates now in kiosk_setting
      await queryInterface.bulkDelete("device", null, { transaction });
      await queryInterface.bulkDelete("room", null, { transaction });
      await queryInterface.bulkDelete("floor", null, { transaction });
      await queryInterface.bulkDelete("building", null, { transaction });
      await queryInterface.bulkDelete("kiosk_group_setting", null, { transaction });
      await queryInterface.bulkDelete("nda_template", null, { transaction });
      await queryInterface.bulkDelete("kiosk_group", null, { transaction });
      await queryInterface.bulkDelete("kiosk_setting", null, { transaction });

      await transaction.commit();
      logger.info("Comprehensive kiosk settings rollback completed successfully");
    } catch (error) {
      await transaction.rollback();
      logger.error("Error during kiosk settings rollback:", error);
      throw error;
    }
  },
};
