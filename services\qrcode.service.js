const QRCode = require('qrcode');
const config = require('../config/config');

/**
 * Generate QR code data URL for device authentication
 * @param {string} deviceId - The device UUID
 * @returns {Promise<string>} Base64 data URL of QR code
 */
const generateDeviceQRCode = async (deviceId) => {
  try {
    // Create the QR code content with device authentication URL
    const qrContent = `${config.server_url.public}/api/facility/devices/authenticate/${deviceId}`;
    
    // Generate QR code as data URL (base64)
    const qrCodeDataURL = await QRCode.toDataURL(qrContent, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      },
      width: 256
    });

    return qrCodeDataURL;
  } catch (error) {
    throw new Error(`Failed to generate QR code: ${error.message}`);
  }
};

module.exports = {
  generateDeviceQRCode,
};
