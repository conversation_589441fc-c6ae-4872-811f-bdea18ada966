const express = require("express");
const DeviceController = require("../controllers/device.controller");
const validate = require("../middlewares/validate");
const DeviceAuthValidation = require("../validations/deviceAuth.validation");

const router = express.Router();

/**
 * @swagger
 * /device/authenticate/{deviceId}:
 *   post:
 *     summary: Authenticate device after QR code scan
 *     tags: [Device Authentication]
 *     parameters:
 *       - in: path
 *         name: deviceId
 *         required: true
 *         schema:
 *           type: string
 *         description: The device ID from QR code
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fingerprint_data
 *             properties:
 *               fingerprint_data:
 *                 type: string
 *                 description: Device fingerprint data from mobile app
 *                 example: "device_hardware_signature_hash"
 *           example:
 *             fingerprint_data: "abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
 *     responses:
 *       200:
 *         description: Device authenticated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Device authenticated successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     access_token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     refresh_token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     device_config:
 *                       type: object
 *                       properties:
 *                         device_id:
 *                           type: string
 *                           example: "64b8f0e2d123e4567890mnop"
 *                         name:
 *                           type: string
 *                           example: "Main Lobby Kiosk 1"
 *                         identifier:
 *                           type: string
 *                           example: "abc123def456ghi789jkl012mno345pqr678stu901vwx234yz"
 *                         kiosk_group:
 *                           type: object
 *                           properties:
 *                             name:
 *                               type: string
 *                               example: "Main Lobby Kiosks"
 *                         facility:
 *                           type: object
 *                           properties:
 *                             name:
 *                               type: string
 *                               example: "Central Hospital"
 *                     permissions:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ["access_kiosk"]
 *       400:
 *         description: Bad request - Missing fingerprint data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Fingerprint data is required"
 *       404:
 *         description: Device not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Device not found"
 */
router.post(
  "/authenticate/:deviceId",
  validate(DeviceAuthValidation.authenticate),
  DeviceController.authenticate
);

module.exports = router;
